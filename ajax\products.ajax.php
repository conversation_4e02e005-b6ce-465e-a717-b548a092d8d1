<?php

require_once dirname(__DIR__) . "/controllers/products.controller.php";
require_once dirname(__DIR__) . "/models/products.model.php";

class AjaxProducts{

  /*=============================================
  EDIT PRODUCT
  =============================================*/ 

  public $idProduct;

  public function ajaxEditProduct(){

    $item = "id";
    $value = $this->idProduct;
    $order = "id";

    $answer = controllerProducts::ctrShowProducts($item, $value, $order);

    echo json_encode($answer);

  }

  /*=============================================
  CREATE PRODUCT
  =============================================*/ 

  public function ajaxCreateProduct(){

    $answer = (new controllerProducts)->ctrCreateProducts();
    
    echo $answer;

  }

}

/*=============================================
EDIT PRODUCT
=============================================*/ 

if(isset($_POST["idProduct"])){

  $editProduct = new AjaxProducts();
  $editProduct -> idProduct = $_POST["idProduct"];
  $editProduct -> ajaxEditProduct();

}

/*=============================================
CREATE PRODUCT
=============================================*/ 

if(isset($_POST["newDescription"])){

	$createProduct = new AjaxProducts();
	$createProduct -> ajaxCreateProduct();

}
