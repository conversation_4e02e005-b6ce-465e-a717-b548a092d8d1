<?php

require_once dirname(__DIR__) . "/controllers/categories.controller.php";
require_once dirname(__DIR__) . "/models/categories.model.php";

class AjaxCategories{

  /*=============================================
  EDIT CATEGORY
  =============================================*/ 

  public $idCategory;

  public function ajaxEditCategory(){

    $item = "id";
    $value = $this->idCategory;

    $answer = ControllerCategories::ctrShowCategories($item, $value);

    echo json_encode($answer);

  }

  /*=============================================
  CREATE CATEGORY
  =============================================*/ 

  public function ajaxCreateCategory(){

    $answer = (new ControllerCategories)->ctrCreateCategory();
    
    echo $answer;

  }

  /*=============================================
  DELETE CATEGORY
  =============================================*/ 

  public function ajaxDeleteCategory(){

    $answer = (new ControllerCategories)->ctrDeleteCategory();
    
    echo $answer;

  }

}

/*=============================================
EDIT CATEGORY
=============================================*/ 

if(isset($_POST["idCategory"]) && !isset($_POST["newCategory"])){

  $editCategory = new AjaxCategories();
  $editCategory -> idCategory = $_POST["idCategory"];
  $editCategory -> ajaxEditCategory();

}

/*=============================================
CREATE CATEGORY
=============================================*/ 

if(isset($_POST["newCategory"])){

	$createCategory = new AjaxCategories();
	$createCategory -> ajaxCreateCategory();

}

/*=============================================
DELETE CATEGORY
=============================================*/ 

if(isset($_POST["idCategory"]) && isset($_POST["deleteCategory"])){

	$deleteCategory = new AjaxCategories();
	$deleteCategory -> idCategory = $_POST["idCategory"];
	$deleteCategory -> ajaxDeleteCategory();

}
